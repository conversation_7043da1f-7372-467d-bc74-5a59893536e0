{"name": "doan", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.292.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "CI=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}}